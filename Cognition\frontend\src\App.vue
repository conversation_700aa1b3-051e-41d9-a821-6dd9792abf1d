<template>
  <div id="app" class="app-container">
    <!-- 全局主题切换按钮 - 仅在非PDQ5页面显示 -->
    <div v-if="!isPDQ5Page" class="theme-toggle-wrapper">
      <ThemeToggle />
    </div>

    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useAuthStore } from '@/stores/auth'
import ThemeToggle from '@/components/ThemeToggle.vue'

// 初始化主题系统
const themeStore = useThemeStore()
const authStore = useAuthStore()
const route = useRoute()

// 检测是否在PDQ5相关页面
const isPDQ5Page = computed(() => {
  return route.path.includes('/pdq5') || route.name?.toString().toLowerCase().includes('pdq5')
})

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()

  // 恢复认证状态
  authStore.restoreAuth()
})
</script>

<style lang="scss">
#app {
  width: 100%;
  min-height: 100vh;
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--background);
  color: var(--foreground);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}

.theme-toggle-wrapper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;

  @media (max-width: $mobile) {
    top: 16px;
    right: 16px;
  }
}

// 确保主题切换时的平滑过渡
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
</style>
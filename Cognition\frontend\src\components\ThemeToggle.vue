<template>
  <button
    @click="handleToggle"
    class="theme-toggle"
    :class="{ 'theme-toggle--dark': isDark }"
    :aria-label="isDark ? '切换到明亮模式' : '切换到暗黑模式'"
    type="button"
  >
    <div class="theme-toggle__icon-container">
      <!-- 太阳图标 (明亮模式) -->
      <svg
        class="theme-toggle__icon theme-toggle__icon--sun"
        :class="{ 'theme-toggle__icon--active': !isDark }"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/>
        <path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41" stroke="currentColor" stroke-width="2"/>
      </svg>
      
      <!-- 月亮图标 (暗黑模式) -->
      <svg
        class="theme-toggle__icon theme-toggle__icon--moon"
        :class="{ 'theme-toggle__icon--active': isDark }"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="2" fill="currentColor"/>
      </svg>
    </div>
    
    <!-- 可选的文字标签 -->
    <span v-if="showLabel" class="theme-toggle__label">
      {{ isDark ? '暗黑模式' : '明亮模式' }}
    </span>
  </button>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/stores/theme'

interface Props {
  showLabel?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  showLabel: false,
  size: 'medium'
})

const themeStore = useThemeStore()
const { isDark, toggleTheme } = themeStore

const handleToggle = () => {
  toggleTheme()
}
</script>

<style lang="scss" scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--foreground);
  font-size: 14px;
  font-weight: 500;
  min-height: 44px; // 确保触摸友好
  
  &:hover {
    background: var(--muted);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  &:focus-visible {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }
}

.theme-toggle__icon-container {
  position: relative;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle__icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  opacity: 0;
  transform: rotate(-90deg) scale(0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  &--active {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
  
  &--sun {
    color: #f59e0b; // 金黄色太阳
  }
  
  &--moon {
    color: #6366f1; // 紫蓝色月亮
  }
}

.theme-toggle__label {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  user-select: none;
}

// 尺寸变体
.theme-toggle--small {
  padding: 6px;
  min-height: 36px;
  
  .theme-toggle__icon-container {
    width: 16px;
    height: 16px;
  }
  
  .theme-toggle__icon {
    width: 16px;
    height: 16px;
  }
  
  .theme-toggle__label {
    font-size: 12px;
  }
}

.theme-toggle--large {
  padding: 12px;
  min-height: 52px;
  
  .theme-toggle__icon-container {
    width: 24px;
    height: 24px;
  }
  
  .theme-toggle__icon {
    width: 24px;
    height: 24px;
  }
  
  .theme-toggle__label {
    font-size: 16px;
  }
}

// 暗黑模式特殊样式
.theme-toggle--dark {
  &:hover {
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
  }
  
  &:active {
    box-shadow: 0 2px 6px rgba(255, 255, 255, 0.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle {
    padding: 10px;
    min-height: 48px; // 移动端更大的触摸目标
  }
}

// 动画关键帧
@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-90deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes rotate-out {
  from {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
  to {
    opacity: 0;
    transform: rotate(90deg) scale(0.8);
  }
}
</style>

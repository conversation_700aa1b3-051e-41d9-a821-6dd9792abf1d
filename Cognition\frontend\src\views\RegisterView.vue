<template>
  <div class="register-container">
    <div class="register-card">
      <!-- 标题区域 -->
      <div class="header-section">
        <router-link to="/login" class="back-button" aria-label="返回登录">
          <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
            <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
          </svg>
        </router-link>
        <h1 class="page-title">用户注册</h1>
        <p class="page-subtitle">创建您的账户</p>
      </div>

      <!-- 注册表单 -->
      <form @submit.prevent="handleRegister" class="register-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="section-title">基本信息</h2>
          
          <div class="form-group">
            <label for="name" class="label required">姓名</label>
            <input
              id="name"
              v-model="registerForm.name"
              type="text"
              placeholder="请输入真实姓名"
              class="input"
              required
              maxlength="20"
            />
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="gender" class="label required">性别</label>
              <select
                id="gender"
                v-model="registerForm.gender"
                class="input"
                required
              >
                <option value="">请选择</option>
                <option value="male">男</option>
                <option value="female">女</option>
              </select>
            </div>

            <div class="form-group">
              <label for="education" class="label required">学历</label>
              <select
                id="education"
                v-model="registerForm.education"
                class="input"
                required
              >
                <option value="">请选择</option>
                <option v-for="edu in educationOptions" :key="edu.value" :value="edu.value">
                  {{ edu.label }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="contactPhone" class="label required">联系电话</label>
            <input
              id="contactPhone"
              v-model="registerForm.contactPhone"
              type="tel"
              placeholder="请输入联系电话"
              class="input"
              required
              pattern="[0-9]{11}"
            />
          </div>


        </div>

        <!-- 测试信息 -->
        <div class="form-section">
          <h2 class="section-title">测试信息</h2>
          
          <div class="form-group">
            <label for="testLocation" class="label required">测试地点</label>
            <select
              id="testLocation"
              v-model="registerForm.testLocation"
              class="input"
              required
            >
              <option value="">请选择</option>
              <option v-for="location in testLocationOptions" :key="location.value" :value="location.value">
                {{ location.label }}
              </option>
            </select>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="dominantHand" class="label required">惯用手</label>
              <select
                id="dominantHand"
                v-model="registerForm.dominantHand"
                class="input"
                required
              >
                <option value="">请选择</option>
                <option value="right">右手</option>
                <option value="left">左手</option>
              </select>
            </div>

            <div class="form-group">
              <fieldset class="radio-group">
                <legend class="label required">是否色盲</legend>
                <div class="radio-options">
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="registerForm.isColorBlind"
                      :value="false"
                      name="colorBlind"
                      class="radio"
                      required
                    />
                    <span class="radio-text">否</span>
                  </label>
                  <label class="radio-label">
                    <input
                      type="radio"
                      v-model="registerForm.isColorBlind"
                      :value="true"
                      name="colorBlind"
                      class="radio"
                      required
                    />
                    <span class="radio-text">是</span>
                  </label>
                </div>
              </fieldset>
            </div>
          </div>
        </div>

        <!-- 用户协议 -->
        <div class="form-section">
          <label class="checkbox-label agreement-label">
            <input
              type="checkbox"
              v-model="agreementAccepted"
              required
              class="checkbox"
            />
            <span class="checkbox-text">
              我已阅读并同意
              <button type="button" class="link-button" @click="showAgreement">
                《用户协议》
              </button>
              和
              <button type="button" class="link-button" @click="showPrivacy">
                《隐私政策》
              </button>
            </span>
          </label>
        </div>

        <!-- 提交按钮 -->
        <button
          type="submit"
          :disabled="isLoading || !isFormValid"
          class="btn btn-primary register-button"
          :aria-busy="isLoading"
        >
          <span v-if="isLoading" class="loading-spinner" aria-hidden="true"></span>
          {{ isLoading ? '注册中...' : '立即注册' }}
        </button>
      </form>

      <!-- 成功提示 -->
      <div
        v-if="successMessage"
        role="alert"
        class="success-message"
        aria-live="polite"
      >
        <div class="success-content">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" class="success-icon">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>
          <div>
            <div class="success-title">注册成功！</div>
            <div class="success-subtitle">您的患者编号：{{ patientNumber }}</div>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div
        v-if="errorMessage"
        role="alert"
        class="error-message"
        aria-live="polite"
      >
        {{ errorMessage }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { educationOptions, testLocationOptions } from '@/utils/mockData'
import type { RegisterData } from '@/types/user'

const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref(false)
const patientNumber = ref('')
const agreementAccepted = ref(false)

const registerForm = ref<RegisterData>({
  name: '',
  education: '',
  gender: 'male',
  contactPhone: '',
  dominantHand: 'right',
  isColorBlind: false,
  testLocation: ''
})

// 计算属性
const isFormValid = computed(() => {
  return registerForm.value.name &&
         registerForm.value.education &&
         registerForm.value.gender &&
         registerForm.value.contactPhone &&
         registerForm.value.dominantHand &&
         registerForm.value.testLocation &&
         (registerForm.value.isColorBlind === true || registerForm.value.isColorBlind === false) &&
         agreementAccepted.value
})

// 处理注册
const handleRegister = async () => {
  if (!isFormValid.value) {
    errorMessage.value = '请填写所有必填项并同意用户协议'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const result = await authStore.register(registerForm.value)
    
    if (result.success) {
      successMessage.value = true
      patientNumber.value = result.patientNumber || ''
      
      // 3秒后跳转到首页
      setTimeout(() => {
        router.push('/home')
      }, 3000)
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '注册失败，请稍后重试'
    console.error('Register error:', error)
  } finally {
    isLoading.value = false
  }
}

// 显示用户协议
const showAgreement = () => {
  // 模拟显示协议内容
  alert('用户协议内容...')
}

// 显示隐私政策
const showPrivacy = () => {
  // 模拟显示隐私政策
  alert('隐私政策内容...')
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    oklch(0.98 0.005 220) 0%,
    oklch(0.96 0.01 240) 100%);
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  justify-content: center;
}

.register-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: $spacing-xl;
  width: 100%;
  max-width: 600px;
  max-height: 95vh;
  overflow-y: auto;
  animation: fadeIn 0.5s ease-out;
}

.header-section {
  text-align: center;
  margin-bottom: $spacing-lg;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  color: oklch(0.5 0.01 240);
  text-decoration: none;
  padding: $spacing-sm;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  border: 1px solid oklch(0.9 0.005 240);
  transition: all 0.3s ease;

  &:hover {
    color: oklch(0.65 0.15 220);
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: oklch(0.25 0.02 240);
  margin-bottom: $spacing-sm;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.page-subtitle {
  color: oklch(0.5 0.01 240);
  font-size: $font-size-sm;
}

.register-form {
  margin-bottom: $spacing-md;
}

.form-section {
  margin-bottom: $spacing-lg;

  &:last-of-type {
    margin-bottom: $spacing-md;
  }
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: oklch(0.3 0.02 240);
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-xs;
  border-bottom: 2px solid oklch(0.85 0.02 220);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240));
    border-radius: 1px;
  }
}

.form-group {
  margin-bottom: $spacing-md;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
  
  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

.label {
  &.required::after {
    content: ' *';
    color: var(--color-destructive);
  }
}

// 输入框透明背景
.register-card {
  input.input,
  select.input,
  textarea.input,
  .input {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: 1px solid oklch(0.88 0.005 240);

    &:focus {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
      border-color: oklch(0.65 0.15 220);
      box-shadow: 0 0 0 2px oklch(0.65 0.15 220 / 0.2);
    }

    &:hover {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
      border-color: oklch(0.8 0.01 240);
    }
  }
}

.checkbox-group,
.radio-group {
  border: none;
  margin: 0;
  padding: 0;
}

.radio-options {
  display: flex;
  gap: $spacing-lg;
  margin-top: $spacing-xs;
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: $font-size-sm;

  .checkbox,
  .radio {
    margin-right: $spacing-sm;
    width: 16px;
    height: 16px;
  }
}

.agreement-label {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(5px);
  border: 1px solid oklch(0.9 0.005 240);
  padding: $spacing-sm;
  border-radius: 12px;
  margin-bottom: $spacing-md;

  .link-button {
    background: none;
    border: none;
    color: oklch(0.65 0.15 220);
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;

    &:hover {
      color: oklch(0.7 0.12 240);
    }
  }
}

.register-button {
  width: 100%;
  background: linear-gradient(135deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240)) !important;
  border: none;
  border-radius: 12px;
  padding: 14px 24px;
  font-weight: 600;
  font-size: $font-size-base;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  margin-top: $spacing-sm;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    transform: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: $spacing-sm;
  }
}

.success-message {
  background: oklch(from var(--color-primary) l c h / 0.1);
  color: var(--color-primary);
  padding: $spacing-lg;
  border-radius: var(--radius);
  border: 1px solid oklch(from var(--color-primary) l c h / 0.2);
  margin-bottom: $spacing-lg;
}

.success-content {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.success-icon {
  flex-shrink: 0;
}

.success-title {
  font-weight: 600;
  margin-bottom: $spacing-xs;
}

.success-subtitle {
  font-size: $font-size-sm;
  opacity: 0.8;
}

.error-message {
  background: oklch(from var(--color-destructive) l c h / 0.1);
  color: var(--color-destructive);
  padding: $spacing-md;
  border-radius: var(--radius);
  border: 1px solid oklch(from var(--color-destructive) l c h / 0.2);
  text-align: center;
  font-size: $font-size-sm;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .register-container {
    padding: $spacing-md;
  }

  .register-card {
    padding: $spacing-xl;
    max-height: 95vh;
  }
}
</style>
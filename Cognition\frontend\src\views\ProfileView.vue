<template>
  <div class="profile-container">
    <!-- 顶部导航 -->
    <header class="profile-header">
      <button @click="$router.back()" class="back-button" aria-label="返回">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
        </svg>
      </button>
      <h1 class="page-title">个人信息</h1>
      <button
        @click="toggleEditMode"
        class="edit-button"
        :class="{ active: isEditing }"
        :aria-label="isEditing ? '保存修改' : '编辑信息'"
      >
        <svg v-if="!isEditing" width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
        </svg>
        <svg v-else width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
        </svg>
      </button>
    </header>

    <main class="profile-content">
      <!-- 头像区域 -->
      <section class="avatar-section">
        <div class="avatar-container">
          <div class="avatar-large">
            <img
              :src="getAvatarPath(user?.gender)"
              :alt="`${user?.name}的头像`"
              class="avatar-img"
            />
          </div>
          <button v-if="isEditing" class="change-avatar-btn" aria-label="更换头像">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
            </svg>
          </button>
        </div>
        <div class="user-basic-info">
          <h2 class="user-name">{{ user?.name }}</h2>
          <p class="patient-number">患者编号：{{ user?.patientNumber }}</p>
          <p class="join-date">注册时间：{{ formatDate(user?.createdAt) }}</p>
        </div>
      </section>

      <!-- 个人信息表单 -->
      <section class="info-form-section">
        <form @submit.prevent="handleSave" class="profile-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">基本信息</h3>
            
            <div class="form-group">
              <label for="name" class="label">姓名</label>
              <input
                id="name"
                v-model="editForm.name"
                type="text"
                class="input"
                :readonly="!isEditing"
                :class="{ readonly: !isEditing }"
                required
                maxlength="20"
              />
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="gender" class="label">性别</label>
                <select
                  id="gender"
                  v-model="editForm.gender"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option value="male">男</option>
                  <option value="female">女</option>
                </select>
              </div>

              <div class="form-group">
                <label for="education" class="label">学历</label>
                <select
                  id="education"
                  v-model="editForm.education"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option v-for="edu in educationOptions" :key="edu.value" :value="edu.value">
                    {{ edu.label }}
                  </option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="contactPhone" class="label">联系电话</label>
              <input
                id="contactPhone"
                v-model="editForm.contactPhone"
                type="tel"
                class="input"
                :readonly="!isEditing"
                :class="{ readonly: !isEditing }"
                pattern="[0-9]{11}"
              />
            </div>


          </div>

          <!-- 测试设置 -->
          <div class="form-section">
            <h3 class="section-title">测试设置</h3>
            
            <div class="form-group">
              <label for="testLocation" class="label">测试地点</label>
              <select
                id="testLocation"
                v-model="editForm.testLocation"
                class="input"
                :disabled="!isEditing"
                :class="{ readonly: !isEditing }"
              >
                <option v-for="location in testLocationOptions" :key="location.value" :value="location.value">
                  {{ location.label }}
                </option>
              </select>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="dominantHand" class="label">惯用手</label>
                <select
                  id="dominantHand"
                  v-model="editForm.dominantHand"
                  class="input"
                  :disabled="!isEditing"
                  :class="{ readonly: !isEditing }"
                >
                  <option value="right">右手</option>
                  <option value="left">左手</option>
                </select>
              </div>

              <div class="form-group">
                <fieldset class="radio-group">
                  <legend class="label">是否色盲</legend>
                  <div class="radio-options">
                    <label class="radio-label">
                      <input
                        type="radio"
                        v-model="editForm.isColorBlind"
                        :value="false"
                        :disabled="!isEditing"
                        name="colorBlind"
                        class="radio"
                      />
                      <span class="radio-text">否</span>
                    </label>
                    <label class="radio-label">
                      <input
                        type="radio"
                        v-model="editForm.isColorBlind"
                        :value="true"
                        :disabled="!isEditing"
                        name="colorBlind"
                        class="radio"
                      />
                      <span class="radio-text">是</span>
                    </label>
                  </div>
                </fieldset>
              </div>
            </div>
          </div>

          <!-- 账户信息 -->
          <div class="form-section">
            <h3 class="section-title">账户信息</h3>
            
            <div class="info-display">
              <div class="info-item">
                <span class="info-label">患者编号</span>
                <span class="info-value">{{ user?.patientNumber }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">注册时间</span>
                <span class="info-value">{{ formatDate(user?.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后登录</span>
                <span class="info-value">{{ formatDate(user?.lastLoginAt) }}</span>
              </div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div v-if="isEditing" class="form-actions">
            <button
              type="button"
              @click="cancelEdit"
              class="btn btn-outline"
              :disabled="isSaving"
            >
              取消
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isSaving || !hasChanges"
              :aria-busy="isSaving"
            >
              <span v-if="isSaving" class="loading-spinner" aria-hidden="true"></span>
              {{ isSaving ? '保存中...' : '保存修改' }}
            </button>
          </div>
        </form>
      </section>
    </main>

    <!-- 成功提示 -->
    <div
      v-if="showSuccessMessage"
      role="alert"
      class="success-toast"
      aria-live="polite"
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
        <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
      </svg>
      个人信息已更新
    </div>

    <!-- 错误提示 -->
    <div
      v-if="errorMessage"
      role="alert"
      class="error-message"
      aria-live="polite"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { educationOptions, testLocationOptions } from '@/utils/mockData'
import type { User } from '@/types/user'
import dayjs from 'dayjs'

const authStore = useAuthStore()

// 响应式数据
const isEditing = ref(false)
const isSaving = ref(false)
const showSuccessMessage = ref(false)
const errorMessage = ref('')

const editForm = ref<Partial<User>>({})
const originalForm = ref<Partial<User>>({})

// 计算属性
const user = computed(() => authStore.user)

const hasChanges = computed(() => {
  if (!user.value) return false
  
  return Object.keys(editForm.value).some(key => {
    const k = key as keyof User
    return editForm.value[k] !== originalForm.value[k]
  })
})

// 方法
const formatDate = (dateString?: string) => {
  if (!dateString) return '-'
  return dayjs(dateString).format('YYYY年MM月DD日 HH:mm')
}

const getAvatarPath = (gender?: string) => {
  return gender === 'female' ? '/images/Female.png' : '/images/Male.png'
}

const initEditForm = () => {
  if (user.value) {
    editForm.value = {
      name: user.value.name,
      gender: user.value.gender,
      education: user.value.education,
      contactPhone: user.value.contactPhone,
      testLocation: user.value.testLocation,
      dominantHand: user.value.dominantHand,
      isColorBlind: user.value.isColorBlind
    }
    originalForm.value = { ...editForm.value }
  }
}

const toggleEditMode = () => {
  if (isEditing.value) {
    handleSave()
  } else {
    isEditing.value = true
    initEditForm()
  }
}

const cancelEdit = () => {
  isEditing.value = false
  editForm.value = { ...originalForm.value }
  errorMessage.value = ''
}

const handleSave = async () => {
  if (!hasChanges.value) {
    isEditing.value = false
    return
  }

  isSaving.value = true
  errorMessage.value = ''

  try {
    const result = await authStore.updateProfile(editForm.value)
    
    if (result.success) {
      isEditing.value = false
      originalForm.value = { ...editForm.value }
      
      // 显示成功提示
      showSuccessMessage.value = true
      setTimeout(() => {
        showSuccessMessage.value = false
      }, 3000)
    } else {
      errorMessage.value = result.message
    }
  } catch (error) {
    errorMessage.value = '保存失败，请稍后重试'
    console.error('Save profile error:', error)
  } finally {
    isSaving.value = false
  }
}

// 监听用户数据变化
watch(user, (newUser) => {
  if (newUser && !isEditing.value) {
    initEditForm()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  initEditForm()
})
</script>

<style lang="scss" scoped>
// 全局样式覆盖 - 最高优先级
:deep(.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(input.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(select.input) {
  background: transparent !important;
  background-color: transparent !important;
}

:deep(textarea.input) {
  background: transparent !important;
  background-color: transparent !important;
}
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    oklch(0.98 0.005 220) 0%,
    oklch(0.96 0.01 240) 100%);
}

.profile-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid oklch(0.92 0.005 240);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
}

.back-button, .edit-button {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid oklch(0.9 0.005 240);
  color: oklch(0.4 0.02 240);
  cursor: pointer;
  padding: $spacing-sm;
  border-radius: 10px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);

  &:hover {
    color: oklch(0.2 0.02 240);
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.active {
    color: oklch(0.65 0.15 220);
    background: rgba(255, 255, 255, 0.95);
    border-color: oklch(0.85 0.05 220);
  }
}

.page-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
}

.profile-content {
  max-width: 800px;
  margin: 0 auto;
  padding: $spacing-xl $spacing-lg;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: $spacing-xl;
  margin-bottom: $spacing-2xl;
  padding: $spacing-2xl;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid oklch(0.94 0.005 240);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  @media (max-width: $mobile) {
    flex-direction: column;
    text-align: center;
    gap: $spacing-lg;
  }
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  .avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.change-avatar-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240));
  color: white;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1) translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
}

.user-name {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--color-foreground);
  margin-bottom: $spacing-sm;
}

.patient-number, .join-date {
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
  margin-bottom: $spacing-xs;
}

.info-form-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid oklch(0.94 0.005 240);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: $spacing-2xl;
}

.profile-form {
  .form-section {
    margin-bottom: $spacing-2xl;

    &:last-of-type {
      margin-bottom: $spacing-lg;
    }
  }

  // 确保所有表单输入元素都没有背景色 - 超高优先级
  .form-section input.input,
  .form-section select.input,
  .form-section textarea.input,
  .form-group input.input,
  .form-group select.input,
  .form-group textarea.input,
  input.input,
  select.input,
  textarea.input,
  input,
  input[type="text"],
  input[type="tel"],
  input[type="email"],
  input[type="number"],
  input[type="password"],
  select,
  textarea {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;

    &:focus {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:hover {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:active {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }

    &:visited {
      background: transparent !important;
      background-color: transparent !important;
      background-image: none !important;
    }
  }
}

.section-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: oklch(0.3 0.02 240);
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-sm;
  border-bottom: 2px solid oklch(0.85 0.02 220);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, oklch(0.65 0.15 220), oklch(0.7 0.12 240));
    border-radius: 1px;
  }
}

.form-group {
  margin-bottom: $spacing-lg;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
  
  @media (max-width: $mobile) {
    grid-template-columns: 1fr;
  }
}

// 强制所有输入框透明背景 - 最高优先级
.profile-container .profile-form input.input,
.profile-container .profile-form select.input,
.profile-container .profile-form textarea.input,
.profile-container input.input,
.profile-container select.input,
.profile-container textarea.input,
.profile-container input,
.profile-container select,
.profile-container textarea,
.profile-container .input {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;

  &:not(.readonly) {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
  }

  &.readonly {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    color: var(--color-muted-foreground);
    cursor: not-allowed;
    border: 1px solid oklch(0.88 0.005 240);
  }
}

// 专门针对下拉控件 - 更高优先级
.profile-container .profile-form select,
.profile-container .profile-form select.input,
.profile-container select,
.profile-container select.input {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;

  option {
    background: white;
    color: #333;
  }
}

.checkbox-group,
.radio-group {
  border: none;
  margin: 0;
  padding: 0;
}

.radio-options {
  display: flex;
  gap: $spacing-lg;
  margin-top: $spacing-xs;
}

.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: $font-size-sm;

  .checkbox,
  .radio {
    margin-right: $spacing-sm;
    width: 16px;
    height: 16px;

    &:disabled {
      cursor: not-allowed;
    }
  }
}

.info-display {
  background: linear-gradient(135deg,
    oklch(0.96 0.005 220) 0%,
    oklch(0.94 0.01 240) 100%);
  border-radius: 12px;
  padding: $spacing-lg;
  border: 1px solid oklch(0.92 0.005 240);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.info-value {
  font-weight: 500;
  color: var(--color-foreground);
}

.form-actions {
  display: flex;
  gap: $spacing-md;
  justify-content: flex-end;
  padding-top: $spacing-lg;
  border-top: 1px solid var(--color-border);
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: $spacing-sm;
}

.success-toast {
  position: fixed;
  top: $spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, oklch(0.65 0.15 140), oklch(0.7 0.12 160));
  color: white;
  padding: $spacing-md $spacing-lg;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

.error-message {
  position: fixed;
  bottom: $spacing-xl;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-destructive);
  color: var(--color-destructive-foreground);
  padding: $spacing-md $spacing-lg;
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-width: 90vw;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translate(-50%, -20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .profile-content {
    padding: $spacing-lg $spacing-md;
  }
  
  .form-actions {
    flex-direction: column-reverse;
    
    .btn {
      width: 100%;
    }
  }
}
</style>
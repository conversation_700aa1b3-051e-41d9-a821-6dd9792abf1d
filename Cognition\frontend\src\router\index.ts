import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/register',
      name: 'Register', 
      component: () => import('@/views/RegisterView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/home',
      name: 'Home',
      component: () => import('@/views/HomeView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('@/views/ProfileView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/test-history',
      name: 'TestHistory',
      component: () => import('@/views/TestHistoryView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tests',
      name: 'Tests',
      component: () => import('@/views/TestListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tests/pdq5/instructions',
      name: 'PDQ5Instructions',
      component: () => import('@/views/PDQ5InstructionsView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tests/pdq5',
      name: 'PDQ5Test',
      component: () => import('@/views/PDQ5TestView.vue'),
      meta: { requiresAuth: true }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isLoggedIn) {
    next('/login')
  } else if ((to.name === 'Login' || to.name === 'Register') && authStore.isLoggedIn) {
    next('/home')
  } else {
    next()
  }
})

export default router
<template>
  <div class="instructions-container test-container">
    <!-- 头部 -->
    <div class="header-section">
      <button @click="goBack" class="back-button" aria-label="返回">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      
      <h1 class="page-title">{{ instructions.title }}</h1>

      <!-- 主题切换按钮 -->
      <ThemeToggle />
    </div>

    <!-- 指导语内容 -->
    <div class="instructions-card">
      <div class="instructions-content">
        <div class="content-header">
          <h2 class="content-title">指导语</h2>
          <button 
            v-if="speechSupported" 
            @click="toggleSpeech" 
            class="speech-button"
            :class="{ active: isSpeaking }"
            :disabled="isLoading"
            aria-label="语音播报"
          >
            <svg v-if="!isSpeaking" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M23 9L17 15M17 9L23 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        
        <div class="instructions-text">
          <p>{{ instructions.content }}</p>
        </div>

        <div class="test-info">
          <div class="info-item">
            <span class="info-label">测试部分：</span>
            <span class="info-value">3个部分</span>
          </div>
          <div class="info-item">
            <span class="info-label">题目数量：</span>
            <span class="info-value">共15题</span>
          </div>
          <div class="info-item">
            <span class="info-label">预计时间：</span>
            <span class="info-value">15-20分钟</span>
          </div>
        </div>

        <div class="sections-preview">
          <h3 class="preview-title">测试内容</h3>
          <div class="sections-list">
            <div class="section-item" v-for="(section, index) in testSections" :key="section.id">
              <div class="section-number">{{ index + 1 }}</div>
              <div class="section-info">
                <h4 class="section-name">{{ section.name }}</h4>
                <p class="section-desc">{{ section.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 开始测试按钮 -->
      <div class="action-section">
        <button 
          @click="startTest" 
          class="start-button"
          :disabled="isLoading"
        >
          <span v-if="!isLoading">{{ instructions.startButtonText }}</span>
          <span v-else class="loading-text">
            <div class="loading-spinner"></div>
            准备中...
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { pdq5Instructions, pdq5TestData } from '@/data/pdq5-test'
import { speechService, SpeechService } from '@/utils/speech'
import ThemeToggle from '@/components/ThemeToggle.vue'

const router = useRouter()

// 响应式数据
const isLoading = ref(false)
const isSpeaking = ref(false)
const speechSupported = ref(SpeechService.isSupported())

// 计时器相关
const startTime = ref<number | null>(null)
const elapsedTime = ref(0)
const timerInterval = ref<number | null>(null)

// 测试数据
const instructions = pdq5Instructions
const testSections = pdq5TestData.sections

// 启动计时器
const startTimer = () => {
  if (startTime.value === null) {
    startTime.value = Date.now()
    timerInterval.value = window.setInterval(() => {
      if (startTime.value) {
        elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
      }
    }, 1000)
  }
}

// 停止计时器
const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

// 返回上一页
const goBack = () => {
  stopTimer()
  speechService.stop()
  router.back()
}

// 切换语音播报
const toggleSpeech = async () => {
  if (isSpeaking.value) {
    speechService.stop()
    isSpeaking.value = false
  } else {
    try {
      isSpeaking.value = true
      await speechService.speak(instructions.content)
      isSpeaking.value = false
    } catch (error) {
      console.error('语音播报失败:', error)
      isSpeaking.value = false
    }
  }
}

// 开始测试
const startTest = () => {
  speechService.stop()
  isLoading.value = true

  // 模拟加载延迟
  setTimeout(() => {
    // 将计时器状态传递给测试页面
    router.push({
      path: '/tests/pdq5',
      query: {
        timerStart: startTime.value?.toString() || '',
        timerElapsed: elapsedTime.value.toString()
      }
    })
  }, 500)
}

// 组件挂载时启动计时器
onMounted(() => {
  startTimer()
})

// 组件卸载时停止语音和计时器
onUnmounted(() => {
  stopTimer()
  speechService.stop()
})
</script>

<style lang="scss" scoped>
.instructions-container {
  height: 100vh;
  background: var(--background);
  padding: $spacing-md;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止滚动条
}

.header-section {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-xl;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: var(--card);
  backdrop-filter: blur(5px);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--muted);
    color: var(--primary);
    transform: translateY(-50%) translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.page-title {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--foreground);
  text-align: center;
  width: 100%;
  margin: 0;
}

.instructions-card {
  background: var(--card);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border);
  padding: $spacing-lg;
  flex: 1;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.6s ease-out;
}

.instructions-content {
  flex: 1;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.content-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--foreground);
  margin: 0;
}

.speech-button {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: $spacing-sm;
  color: var(--muted-foreground);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: var(--muted);
    color: var(--primary);
    transform: translateY(-1px);
  }

  &.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
}

.instructions-text {
  font-size: $font-size-base;
  line-height: 1.6;
  color: var(--foreground);
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: var(--muted);
  border-radius: 12px;
  border-left: 4px solid var(--primary);

  p {
    color: var(--foreground) !important;
    margin: 0;
  }
}

.test-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: $spacing-sm;
  margin-bottom: $spacing-lg;
}

.info-item {
  background: var(--card);
  padding: $spacing-sm;
  border-radius: 8px;
  border: 1px solid var(--border);
}

.info-label {
  font-weight: 600;
  color: var(--muted-foreground);
}

.info-value {
  color: var(--foreground);
  font-weight: 500;
}

.sections-preview {
  margin-bottom: $spacing-lg;
  flex: 1;
  overflow-y: auto;
}

.preview-title {
  font-size: $font-size-base;
  font-weight: 600;
  color: var(--foreground);
  margin-bottom: $spacing-sm;
}

.sections-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.section-item {
  display: flex;
  align-items: flex-start;
  gap: $spacing-sm;
  padding: $spacing-sm;
  background: var(--muted);
  border-radius: 8px;
  border: 1px solid var(--border);
}

.section-number {
  width: 24px;
  height: 24px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: $font-size-sm;
  flex-shrink: 0;
}

.section-info {
  flex: 1;
}

.section-name {
  font-size: $font-size-sm;
  font-weight: 600;
  color: var(--foreground);
  margin: 0 0 2px 0;
}

.section-desc {
  font-size: $font-size-xs;
  color: var(--muted-foreground);
  margin: 0;
  line-height: 1.4;
}

.action-section {
  margin-top: $spacing-md;
  padding-top: $spacing-md;
  border-top: 1px solid var(--border);
  flex-shrink: 0;
}

.start-button {
  width: 100%;
  background: var(--primary);
  border: none;
  border-radius: 16px;
  padding: 18px 32px;
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--primary-foreground);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-lg);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: $mobile) {
  .instructions-container {
    padding: $spacing-sm;
  }

  .instructions-card {
    padding: $spacing-md;
    margin-top: $spacing-sm;
  }

  .test-info {
    grid-template-columns: 1fr;
    gap: $spacing-xs;
  }

  .section-item {
    flex-direction: row;
    padding: $spacing-xs;
  }

  .section-number {
    width: 20px;
    height: 20px;
  }
}
</style>

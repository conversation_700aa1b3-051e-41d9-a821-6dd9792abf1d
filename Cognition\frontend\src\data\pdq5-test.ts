// PDQ-5 测试数据配置
import type { PDQ5TestData, TestInstructions } from '@/types/test'

// 测试指导语
export const pdq5Instructions: TestInstructions = {
  title: 'PDQ-5测试',
  content: '以下这些描述的是人们在记忆力、注意力或集中度方面可能遇到的问题。请根据过去 7 天的情况选择最适合自己的答案，根据自己的情况，在相应答案上打"√"。',
  audioEnabled: true,
  startButtonText: '开始测试'
}

// PDQ-5 测试数据
export const pdq5TestData: PDQ5TestData = {
  sections: [
    {
      id: 'memory_attention',
      name: '记忆力注意力调查',
      description: '题目描述人们在记忆力、注意力或集中度方面可能遇到的问题',
      instructions: '请根据过去 7 天的情况选择最适合自己的答案，根据自己的情况，选择相应答案。',
      options: [
        { value: 1, text: '过去7天从来没有', description: '过去7天从来没有' },
        { value: 2, text: '很少(1-2次)', description: '很少(1-2次)' },
        { value: 3, text: '有时(3-5次)', description: '有时(3-5次)' },
        { value: 4, text: '经常(几乎每天一次)', description: '经常(几乎每天一次)' },
        { value: 5, text: '很经常(大于每天一次)', description: '很经常(大于每天一次)' }
      ],
      questions: [
        {
          id: 'ma_001',
          text: '难以做到井井有条?',
          options: [],
          section: 'memory_attention',
          order: 1
        },
        {
          id: 'ma_002',
          text: '阅读的时候很难集中注意力?',
          options: [],
          section: 'memory_attention',
          order: 2
        },
        {
          id: 'ma_003',
          text: '忘记日期，除非你看过?',
          options: [],
          section: 'memory_attention',
          order: 3
        },
        {
          id: 'ma_004',
          text: '打完电话之后忘记交谈内容?',
          options: [],
          section: 'memory_attention',
          order: 4
        },
        {
          id: 'ma_005',
          text: '感觉大脑一片空白?',
          options: [],
          section: 'memory_attention',
          order: 5
        }
      ]
    },
    {
      id: 'thinking_reaction',
      name: '思维反应调查',
      description: '当心情低落、感觉抑郁时，人们会思考和做很多不同的事情',
      instructions: '用 1-4 级评分回答题目问题，（1）几乎从不，（2）有时，（3）经常，（4）几乎总是。需要指出一般情况下自己是怎么做的，而不是自己认为应该怎么做。',
      options: [
        { value: 1, text: '几乎从不', description: '几乎从不这样做' },
        { value: 2, text: '有时', description: '有时会这样做' },
        { value: 3, text: '经常', description: '经常这样做' },
        { value: 4, text: '几乎总是', description: '几乎总是这样做' }
      ],
      questions: [
        {
          id: 'tr_001',
          text: '思考"为什么我总是这样反应？"',
          options: [],
          section: 'thinking_reaction',
          order: 1
        },
        {
          id: 'tr_002',
          text: '想着自己的缺点、失误、错误或不足',
          options: [],
          section: 'thinking_reaction',
          order: 2
        },
        {
          id: 'tr_003',
          text: '思考自己感觉有多孤独',
          options: [],
          section: 'thinking_reaction',
          order: 3
        },
        {
          id: 'tr_004',
          text: '思考自己的问题并试图理解它们',
          options: [],
          section: 'thinking_reaction',
          order: 4
        },
        {
          id: 'tr_005',
          text: '反复思考最近发生的不愉快事件',
          options: [],
          section: 'thinking_reaction',
          order: 5
        }
      ]
    },
    {
      id: 'cognitive_flexibility',
      name: '认知灵活性',
      description: '关于人对自身行为的看法和感觉的问卷',
      instructions: '需对照自己与每一项的符合程度，按以下标准进行选择:1表示"从不"，2表示"很少"，3表示"有时"，4表示"经常"，5表示"总是"。',
      options: [
        { value: 1, text: '从不', description: '从不符合' },
        { value: 2, text: '很少', description: '很少符合' },
        { value: 3, text: '有时', description: '有时符合' },
        { value: 4, text: '经常', description: '经常符合' },
        { value: 5, text: '总是', description: '总是符合' }
      ],
      questions: [
        {
          id: 'cf_001',
          text: '我能够很好地适应变化',
          options: [],
          section: 'cognitive_flexibility',
          order: 1
        },
        {
          id: 'cf_002',
          text: '当计划改变时，我能够快速调整',
          options: [],
          section: 'cognitive_flexibility',
          order: 2
        },
        {
          id: 'cf_003',
          text: '我能从多个角度看待问题',
          options: [],
          section: 'cognitive_flexibility',
          order: 3
        },
        {
          id: 'cf_004',
          text: '面对困难时，我能想出多种解决方案',
          options: [],
          section: 'cognitive_flexibility',
          order: 4
        },
        {
          id: 'cf_005',
          text: '我能够接受不同的观点和意见',
          options: [],
          section: 'cognitive_flexibility',
          order: 5
        }
      ]
    }
  ],
  totalQuestions: 15
}

// 为每个问题分配对应的选项
pdq5TestData.sections.forEach(section => {
  section.questions.forEach(question => {
    question.options = section.options
  })
})

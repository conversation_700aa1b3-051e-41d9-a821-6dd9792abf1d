import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export type ThemeMode = 'light' | 'dark' | 'system'

export const useThemeStore = defineStore('theme', () => {
  // 主题模式状态
  const mode = ref<ThemeMode>('system')
  
  // 系统主题偏好
  const systemPrefersDark = ref(false)
  
  // 计算当前实际主题
  const isDark = computed(() => {
    if (mode.value === 'system') {
      return systemPrefersDark.value
    }
    return mode.value === 'dark'
  })
  
  // 当前主题名称
  const currentTheme = computed(() => isDark.value ? 'dark' : 'light')
  
  // 初始化系统主题检测
  const initSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      systemPrefersDark.value = mediaQuery.matches
      
      // 监听系统主题变化
      mediaQuery.addEventListener('change', (e) => {
        systemPrefersDark.value = e.matches
      })
    }
  }
  
  // 应用主题到DOM
  const applyTheme = (theme: 'light' | 'dark') => {
    if (typeof document !== 'undefined') {
      const root = document.documentElement

      // 移除之前的主题类
      root.classList.remove('light', 'dark')

      // 添加新的主题类
      root.classList.add(theme)

      // 设置data属性用于CSS选择器
      root.setAttribute('data-theme', theme)
    }
  }
  
  // 设置主题模式
  const setMode = (newMode: ThemeMode) => {
    mode.value = newMode
    localStorage.setItem('theme-mode', newMode)
  }
  
  // 切换主题
  const toggleTheme = () => {
    if (mode.value === 'light') {
      setMode('dark')
    } else if (mode.value === 'dark') {
      setMode('light')
    } else {
      // 如果是system模式，切换到相反的固定模式
      setMode(systemPrefersDark.value ? 'light' : 'dark')
    }
  }
  
  // 从localStorage恢复主题设置
  const restoreTheme = () => {
    const savedMode = localStorage.getItem('theme-mode') as ThemeMode
    if (savedMode && ['light', 'dark', 'system'].includes(savedMode)) {
      mode.value = savedMode
    }
  }
  
  // 初始化主题系统
  const initTheme = () => {
    initSystemTheme()
    restoreTheme()
  }
  
  // 监听主题变化并应用到DOM
  watch(isDark, (dark) => {
    applyTheme(dark ? 'dark' : 'light')
  }, { immediate: true })
  
  return {
    mode,
    isDark,
    currentTheme,
    systemPrefersDark,
    setMode,
    toggleTheme,
    initTheme,
    applyTheme
  }
})

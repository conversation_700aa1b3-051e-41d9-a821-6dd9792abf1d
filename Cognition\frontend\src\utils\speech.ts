// 语音播报工具函数
import type { VoiceSettings } from '@/types/test'

// 默认语音设置
const defaultVoiceSettings: VoiceSettings = {
  enabled: true,
  rate: 0.8,
  pitch: 1.0,
  volume: 0.8
}

// 语音播报类
export class SpeechService {
  private synthesis: SpeechSynthesis
  private currentUtterance: SpeechSynthesisUtterance | null = null
  private settings: VoiceSettings
  private voices: SpeechSynthesisVoice[] = []

  constructor(settings: Partial<VoiceSettings> = {}) {
    this.synthesis = window.speechSynthesis
    this.settings = { ...defaultVoiceSettings, ...settings }
    this.loadVoices()
  }

  // 加载可用语音
  private loadVoices() {
    const loadVoicesHandler = () => {
      this.voices = this.synthesis.getVoices()
      // 优先选择中文语音
      const chineseVoice = this.voices.find(voice => 
        voice.lang.includes('zh') || voice.name.includes('Chinese')
      )
      if (chineseVoice) {
        this.settings.voice = chineseVoice
      }
    }

    // 语音列表可能异步加载
    if (this.synthesis.getVoices().length > 0) {
      loadVoicesHandler()
    } else {
      this.synthesis.addEventListener('voiceschanged', loadVoicesHandler)
    }
  }

  // 播报文本
  speak(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.settings.enabled) {
        resolve()
        return
      }

      // 停止当前播报
      this.stop()

      const utterance = new SpeechSynthesisUtterance(text)
      utterance.rate = this.settings.rate
      utterance.pitch = this.settings.pitch
      utterance.volume = this.settings.volume
      
      if (this.settings.voice) {
        utterance.voice = this.settings.voice
      }

      utterance.onend = () => {
        this.currentUtterance = null
        resolve()
      }

      utterance.onerror = (event) => {
        this.currentUtterance = null
        reject(new Error(`Speech synthesis error: ${event.error}`))
      }

      this.currentUtterance = utterance
      this.synthesis.speak(utterance)
    })
  }

  // 停止播报
  stop() {
    if (this.currentUtterance) {
      this.synthesis.cancel()
      this.currentUtterance = null
    }
  }

  // 暂停播报
  pause() {
    if (this.synthesis.speaking && !this.synthesis.paused) {
      this.synthesis.pause()
    }
  }

  // 恢复播报
  resume() {
    if (this.synthesis.paused) {
      this.synthesis.resume()
    }
  }

  // 检查是否正在播报
  isSpeaking(): boolean {
    return this.synthesis.speaking
  }

  // 检查是否暂停
  isPaused(): boolean {
    return this.synthesis.paused
  }

  // 更新设置
  updateSettings(newSettings: Partial<VoiceSettings>) {
    this.settings = { ...this.settings, ...newSettings }
  }

  // 获取可用语音列表
  getVoices(): SpeechSynthesisVoice[] {
    return this.voices
  }

  // 检查浏览器是否支持语音合成
  static isSupported(): boolean {
    return 'speechSynthesis' in window
  }
}

// 创建全局语音服务实例
export const speechService = new SpeechService()

// 便捷函数
export const speak = (text: string) => speechService.speak(text)
export const stopSpeech = () => speechService.stop()
export const pauseSpeech = () => speechService.pause()
export const resumeSpeech = () => speechService.resume()
